package com.ibms.service.realty.web.controller.cleaningRecord;


import com.ibms.service.realty.web.domain.CleaningRecord;
import com.ibms.service.realty.web.service.CleaningRecordService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 保洁记录Controller
 *
 * <AUTHOR>
 * @date 2023-07-04
 */
@RestController
@RequestMapping("/cleaningRecord")
@Api(tags = "保洁记录")
public class CleaningRecordController extends BaseController {
    @Autowired
    private CleaningRecordService cleaningRecordService;

    /**
     * 查询保洁记录列表
     */
    @GetMapping("/list")
    @ApiOperation("查询保洁记录列表")
    public TableDataInfo list(CleaningRecord cleaningRecord) {
        startPage();
        List<CleaningRecord> list = cleaningRecordService.selectCleaningRecordList(cleaningRecord);
        return getDataTable(list);
    }

    /**
     * 导出保洁记录列表
     */
    @Log(title = "保洁记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出保洁记录列表")
    public void export(HttpServletResponse response, CleaningRecord cleaningRecord) {
        List<CleaningRecord> list = cleaningRecordService.selectCleaningRecordList(cleaningRecord);
        ExcelUtil<CleaningRecord> util = new ExcelUtil<CleaningRecord>(CleaningRecord.class);
        util.exportExcel(response, list, "保洁记录数据");
    }

    /**
     * 获取保洁记录详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取保洁记录详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(cleaningRecordService.selectCleaningRecordById(id));
    }

    /**
     * 新增保洁记录
     */
    @Log(title = "保洁记录", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增保洁记录")
    public AjaxResult add(@RequestBody CleaningRecord cleaningRecord) {
        return toAjax(cleaningRecordService.insertCleaningRecord(cleaningRecord));
    }

    /**
     * 修改保洁记录
     */
    @Log(title = "保洁记录", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改保洁记录")
    public AjaxResult edit(@RequestBody CleaningRecord cleaningRecord) {
        return toAjax(cleaningRecordService.updateCleaningRecord(cleaningRecord));
    }

    /**
     * 删除保洁记录
     */
    @Log(title = "保洁记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除保洁记录")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(cleaningRecordService.deleteCleaningRecordByIds(ids));
    }
}
