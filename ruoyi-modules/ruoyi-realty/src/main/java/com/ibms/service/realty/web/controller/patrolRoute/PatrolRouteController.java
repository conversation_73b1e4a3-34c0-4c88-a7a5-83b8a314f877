package com.ibms.service.realty.web.controller.patrolRoute;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.ibms.service.realty.web.domain.PatrolRoute;
import com.ibms.service.realty.web.service.PatrolRouteService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 巡更线路Controller
 *
 * <AUTHOR>
 * @date 2023-04-11
 */
@RestController
@RequestMapping("/patrolRoute")
public class PatrolRouteController extends BaseController {
    /**
     * 资源映射路径 前缀
     */
    @Value("${file.prefix}")
    public String localFilePrefix;
    /**
     * 域名或本机访问地址
     */
    @Value("${file.domain}")
    public String domain;
    @Autowired
    private PatrolRouteService patrolRouteService;
    /**
     * 上传文件存储在本地的根路径
     */
    @Value("${file.path}")
    private String localFilePath;

    /**
     * 查询巡更线路列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PatrolRoute patrolRoute) {
        startPage();
        List<PatrolRoute> list = patrolRouteService.selectPatrolRouteList(patrolRoute);
        return getDataTable(list);
    }

    /**
     * 导出巡更线路列表
     */
    @Log(title = "巡更线路", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PatrolRoute patrolRoute) {
        List<PatrolRoute> list = patrolRouteService.selectPatrolRouteList(patrolRoute);
        ExcelUtil<PatrolRoute> util = new ExcelUtil<PatrolRoute>(PatrolRoute.class);
        util.exportExcel(response, list, "巡更线路数据");
    }

    /**
     * 获取巡更线路详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(patrolRouteService.selectPatrolRouteById(id));
    }

    /**
     * 新增巡更线路
     */
    @Log(title = "巡更线路", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PatrolRoute patrolRoute) {
        return toAjax(patrolRouteService.insertPatrolRoute(patrolRoute));
    }

    /**
     * 修改巡更线路
     */
    @Log(title = "巡更线路", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PatrolRoute patrolRoute) {
        return toAjax(patrolRouteService.updatePatrolRoute(patrolRoute));
    }

    /**
     * 作废/恢复
     */
    @Log(title = "作废/恢复", businessType = BusinessType.UPDATE)
    @PutMapping("/operator")
    public AjaxResult operator(@RequestBody PatrolRoute patrolRoute) {
        return toAjax(patrolRouteService.operator(patrolRoute));
    }

    /**
     * 删除巡更线路
     */
    @Log(title = "巡更线路", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(patrolRouteService.deletePatrolRouteByIds(ids));
    }

    /**
     * 生成二维码
     */
    @ApiOperation("生成二维码")
    @GetMapping(value = "/createQRCode")
    public AjaxResult createQRCode(Integer id) {
        List<Map<String, Object>> dataList = new ArrayList();
        // 根据id查询点位
        PatrolRoute route = patrolRouteService.selectPatrolRouteById(id);
        route.getPatrolPointsList().stream().forEach(a -> {
            String returnUrl = createQr(a.getCode());
            String pointName = a.getName();
            Map<String, Object> data = new HashMap();
            data.put("returnUrl", returnUrl);
            data.put("pointName", pointName);
            dataList.add(data);
        });
        return AjaxResult.success("生成二维码成功", dataList);
    }

    private String createQr(String content) {
        LocalDate now = LocalDate.now();
        String fileName = System.currentTimeMillis() + RandomUtil.randomInt(0, 100) + ".jpg";
        String savePath = localFilePath + "/" + now.getYear() + "/" + now.getMonthValue() + "/" + now.getDayOfMonth() + "/" + fileName;
        String returnUrl = domain + localFilePrefix + "/" + now.getYear() + "/" + now.getMonthValue() + "/" + now.getDayOfMonth() + "/" + fileName;
        FileUtil.mkdir(savePath.substring(0, savePath.lastIndexOf("/")));
        QrCodeUtil.generate(content, 300, 300, FileUtil.file(savePath));
        return returnUrl;
    }
}
