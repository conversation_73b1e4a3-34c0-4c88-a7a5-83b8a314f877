package com.ibms.service.realty.web.controller.cleaningPlan;

import com.ibms.service.realty.web.domain.CleaningPlan;
import com.ibms.service.realty.web.service.CleaningPlanService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 保洁巡查计划Controller
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@RestController
@RequestMapping("/cleanPlan")
@Api(tags = "保洁巡查计划")
public class CleaningPlanController extends BaseController {
    @Autowired
    private CleaningPlanService cleaningPlanService;

    /**
     * 查询保洁巡查计划列表
     */
    @GetMapping("/list")
    @ApiOperation("查询保洁巡查计划列表")
    public TableDataInfo list(CleaningPlan cleaningPlan) {
        startPage();
        List<CleaningPlan> list = cleaningPlanService.selectCleaningPlanList(cleaningPlan);
        return getDataTable(list);
    }

    /**
     * 导出保洁巡查计划列表
     */
    @Log(title = "保洁巡查计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出保洁巡查计划列表")
    public void export(HttpServletResponse response, CleaningPlan cleaningPlan) {
        List<CleaningPlan> list = cleaningPlanService.selectCleaningPlanList(cleaningPlan);
        ExcelUtil<CleaningPlan> util = new ExcelUtil<CleaningPlan>(CleaningPlan.class);
        util.exportExcel(response, list, "保洁巡查计划数据");
    }

    /**
     * 获取保洁巡查计划详细信息
     */
    @GetMapping(value = "/{taskId}")
    @ApiOperation("获取保洁巡查计划详细信息")
    public AjaxResult getInfo(@PathVariable("taskId") String taskId) {
        return AjaxResult.success(cleaningPlanService.selectCleaningPlanByTaskId(taskId));
    }

    /**
     * 新增保洁巡查计划
     */
    @Log(title = "保洁巡查计划", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增保洁巡查计划")
    public AjaxResult add(@RequestBody CleaningPlan cleaningPlan) {
        return toAjax(cleaningPlanService.insertCleaningPlan(cleaningPlan));
    }

    /**
     * 修改保洁巡查计划
     */
    @Log(title = "保洁巡查计划", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改保洁巡查计划")
    public AjaxResult edit(@RequestBody CleaningPlan cleaningPlan) {
        return toAjax(cleaningPlanService.updateCleaningPlan(cleaningPlan));
    }


    /**
     * 停用/启用
     */
    @Log(title = "停用/启用", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    @ApiOperation("停用/启用")
    public AjaxResult changeStatus(@RequestBody CleaningPlan cleaningPlan) {
        return toAjax(cleaningPlanService.changeStatus(cleaningPlan));
    }

    /**
     * 删除保洁巡查计划
     */
    @Log(title = "保洁巡查计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskIds}")
    @ApiOperation("删除保洁巡查计划")
    public AjaxResult remove(@PathVariable String[] taskIds) {
        return toAjax(cleaningPlanService.deleteCleaningPlanByTaskIds(taskIds));
    }
}

