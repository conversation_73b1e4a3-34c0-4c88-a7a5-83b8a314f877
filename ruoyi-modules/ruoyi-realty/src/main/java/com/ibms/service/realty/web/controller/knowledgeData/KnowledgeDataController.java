package com.ibms.service.realty.web.controller.knowledgeData;

import com.ibms.service.realty.web.domain.KnowledgeData;
import com.ibms.service.realty.web.service.KnowledgeDataService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 专家知识库Controller
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@RestController
@RequestMapping("/knowledgeData")
@Api(tags = "专家知识库")
public class KnowledgeDataController extends BaseController {
    @Autowired
    private KnowledgeDataService knowledgeDataService;

    /**
     * 查询专家知识库列表
     */
    @GetMapping("/list")
    @ApiOperation("查询专家知识库列表")
    public TableDataInfo list(KnowledgeData knowledgeData) {
        startPage();
        List<KnowledgeData> list = knowledgeDataService.selectKnowledgeDataList(knowledgeData);
        return getDataTable(list);
    }

    /**
     * 导出专家知识库列表
     */
    @Log(title = "专家知识库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出专家知识库列表")
    public void export(HttpServletResponse response, KnowledgeData knowledgeData) {
        List<KnowledgeData> list = knowledgeDataService.selectKnowledgeDataList(knowledgeData);
        ExcelUtil<KnowledgeData> util = new ExcelUtil<KnowledgeData>(KnowledgeData.class);
        util.exportExcel(response, list, "专家知识库数据");
    }

    /**
     * 获取专家知识库详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取专家知识库详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(knowledgeDataService.selectKnowledgeDataById(id));
    }

    /**
     * 新增专家知识库
     */
    @Log(title = "专家知识库", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增专家知识库")
    public AjaxResult add(@RequestBody KnowledgeData knowledgeData) {
        return toAjax(knowledgeDataService.insertKnowledgeData(knowledgeData));
    }

    /**
     * 修改专家知识库
     */
    @Log(title = "专家知识库", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改专家知识库")
    public AjaxResult edit(@RequestBody KnowledgeData knowledgeData) {
        return toAjax(knowledgeDataService.updateKnowledgeData(knowledgeData));
    }

    /**
     * 删除专家知识库
     */
    @Log(title = "专家知识库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除专家知识库")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(knowledgeDataService.deleteKnowledgeDataByIds(ids));
    }
}
