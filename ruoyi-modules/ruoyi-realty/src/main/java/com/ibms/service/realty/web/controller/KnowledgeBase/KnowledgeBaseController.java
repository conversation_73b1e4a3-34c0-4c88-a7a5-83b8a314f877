package com.ibms.service.realty.web.controller.KnowledgeBase;

import com.ibms.service.realty.web.domain.KnowledgeBase;
import com.ibms.service.realty.web.service.KnowledgeBaseService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 知识库Controller
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
@RestController
@RequestMapping("/knowledgeBase")
@Api(tags = "知识库")
public class KnowledgeBaseController extends BaseController {
    @Autowired
    private KnowledgeBaseService knowledgeBaseService;

    /**
     * 查询知识库列表
     */
    @GetMapping("/list")
    @ApiOperation("查询知识库列表")
    public TableDataInfo list(KnowledgeBase knowledgeBase) {
        startPage();
        List<KnowledgeBase> list = knowledgeBaseService.selectKnowledgeBaseList(knowledgeBase);
        return getDataTable(list);
    }

    /**
     * 导出知识库列表
     */
    @Log(title = "知识库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出知识库列表")
    public void export(HttpServletResponse response, KnowledgeBase knowledgeBase) {
        List<KnowledgeBase> list = knowledgeBaseService.selectKnowledgeBaseList(knowledgeBase);
        ExcelUtil<KnowledgeBase> util = new ExcelUtil<KnowledgeBase>(KnowledgeBase.class);
        util.exportExcel(response, list, "知识库数据");
    }

    /**
     * 获取知识库详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation("获取知识库详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(knowledgeBaseService.selectKnowledgeBaseById(id));
    }

    /**
     * 新增知识库
     */
    @Log(title = "知识库", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增知识库")
    public AjaxResult add(@RequestBody KnowledgeBase knowledgeBase) {
        return toAjax(knowledgeBaseService.insertKnowledgeBase(knowledgeBase));
    }

    /**
     * 修改知识库
     */
    @Log(title = "知识库", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改知识库")
    public AjaxResult edit(@RequestBody KnowledgeBase knowledgeBase) {
        return toAjax(knowledgeBaseService.updateKnowledgeBase(knowledgeBase));
    }

    /**
     * 删除知识库
     */
    @Log(title = "知识库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    @ApiOperation("删除知识库")
    public AjaxResult remove(@PathVariable Integer id) {
        return toAjax(knowledgeBaseService.deleteKnowledgeBaseById(id));
    }

    /**
     * 层级根列表
     */
    @GetMapping("/levelListRoot")
    @ApiOperation("层级根列表")
    public AjaxResult levelListRoot() {
        return AjaxResult.success(knowledgeBaseService.levelListRoot());
    }
}
