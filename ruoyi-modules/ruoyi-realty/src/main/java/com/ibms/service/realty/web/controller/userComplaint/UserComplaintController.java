// package com.ibms.service.realty.web.controller.userComplaint;
//
// import com.ibms.service.realty.web.domain.UserComplaint;
// import com.ibms.service.realty.web.service.UserComplaintService;
// import com.ruoyi.common.core.utils.poi.ExcelUtil;
// import com.ruoyi.common.core.web.controller.BaseController;
// import com.ruoyi.common.core.web.domain.AjaxResult;
// import com.ruoyi.common.core.web.page.TableDataInfo;
// import com.ruoyi.common.log.annotation.Log;
// import com.ruoyi.common.log.enums.BusinessType;
// import io.swagger.annotations.Api;
// import io.swagger.annotations.ApiOperation;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.web.bind.annotation.*;
//
// import javax.servlet.http.HttpServletResponse;
// import java.util.List;
//
/// **
// * 用户投诉Controller
// *
// * <AUTHOR>
// * @date 2023-05-26
// */
//@RestController
//@RequestMapping("/userComplaint")
//@Api(tags = "用户投诉")
// public class UserComplaintController extends BaseController {
//    @Autowired
//    private UserComplaintService userComplaintService;
//
//    /**
//     * 查询用户投诉列表
//     */
//    @GetMapping("/list")
//    @ApiOperation(value = "查询用户投诉列表")
//    public TableDataInfo list(UserComplaint userComplaint) {
//        startPage();
//        List<UserComplaint> list = userComplaintService.selectUserComplaintList(userComplaint);
//        return getDataTable(list);
//    }
//
//    /**
//     * 导出用户投诉列表
//     */
//    @Log(title = "用户投诉", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    @ApiOperation(value = "导出用户投诉列表")
//    public void export(HttpServletResponse response, UserComplaint userComplaint) {
//        List<UserComplaint> list = userComplaintService.selectUserComplaintList(userComplaint);
//        ExcelUtil<UserComplaint> util = new ExcelUtil<UserComplaint>(UserComplaint.class);
//        util.exportExcel(response, list, "用户投诉数据");
//    }
//
//    /**
//     * 获取用户投诉详细信息
//     */
//    @GetMapping(value = "/{id}")
//    @ApiOperation(value = "获取用户投诉详细信息")
//    public AjaxResult getInfo(@PathVariable("id") Integer id) {
//        return AjaxResult.success(userComplaintService.selectUserComplaintById(id));
//    }
//
//    /**
//     * 新增用户投诉
//     */
//    @Log(title = "用户投诉", businessType = BusinessType.INSERT)
//    @PostMapping
//    @ApiOperation(value = "新增用户投诉")
//    public AjaxResult add(@RequestBody UserComplaint userComplaint) {
//        return toAjax(userComplaintService.insertUserComplaint(userComplaint));
//    }
//
//    /**
//     * 修改用户投诉
//     */
//    @Log(title = "用户投诉", businessType = BusinessType.UPDATE)
//    @PutMapping
//    @ApiOperation(value = "修改用户投诉")
//    public AjaxResult edit(@RequestBody UserComplaint userComplaint) {
//        return toAjax(userComplaintService.updateUserComplaint(userComplaint));
//    }
//
//    /**
//     * 删除用户投诉
//     */
//    @Log(title = "用户投诉", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    @ApiOperation(value = "删除用户投诉")
//    public AjaxResult remove(@PathVariable Integer[] ids) {
//        return toAjax(userComplaintService.deleteUserComplaintByIds(ids));
//    }
//}
