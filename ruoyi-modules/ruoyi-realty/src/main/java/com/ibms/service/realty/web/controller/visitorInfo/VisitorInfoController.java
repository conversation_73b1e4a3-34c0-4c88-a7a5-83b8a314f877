package com.ibms.service.realty.web.controller.visitorInfo;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.ibms.service.realty.web.domain.VisitorInfo;
import com.ibms.service.realty.web.service.VisitorInfoService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 访客信息Controller
 *
 * <AUTHOR>
 * @date 2023-05-25
 */
@RestController
@RequestMapping("/visitor")
@Api(tags = "访客信息Controller")
public class VisitorInfoController extends BaseController {
    /**
     * 资源映射路径 前缀
     */
    @Value("${file.prefix}")
    public String localFilePrefix;
    /**
     * 文件域名
     */
    @Value("${file.domain}")
    public String domain;
    @Autowired
    private VisitorInfoService visitorInfoService;
    /**
     * 上传文件存储在本地的根路径
     */
    @Value("${file.path}")
    private String localFilePath;

    /**
     * 前端ng域名
     */
    @Value("${front-domain}")
    private String frontDomain;

    /**
     * 查询访客信息列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "访客信息列表")
    public TableDataInfo list(VisitorInfo visitorInfo) {
        startPage();
        List<VisitorInfo> list = visitorInfoService.selectVisitorInfoList(visitorInfo);
        return getDataTable(list);
    }

    /**
     * 导出访客信息列表
     */
    @Log(title = "访客信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出访客信息列表")
    public void export(HttpServletResponse response, VisitorInfo visitorInfo) {
        List<VisitorInfo> list = visitorInfoService.selectVisitorInfoList(visitorInfo);
        ExcelUtil<VisitorInfo> util = new ExcelUtil<VisitorInfo>(VisitorInfo.class);
        util.exportExcel(response, list, "访客信息数据");
    }

    /**
     * 获取访客信息详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取访客信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(visitorInfoService.selectVisitorInfoById(id));
    }

    /**
     * 新增访客信息
     */
    @Log(title = "访客信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增访客信息")
    public AjaxResult add(@RequestBody VisitorInfo visitorInfo) {
        visitorInfo.setCreateTime(new Date());
        if (visitorInfo.getCreateUserId() == null) {
            visitorInfo.setCreateUserId(SecurityUtils.getUserId());
        }
        return toAjax(visitorInfoService.insertVisitorInfo(visitorInfo));
    }

    /**
     * 扫码新增访客信息
     */
    @Log(title = "扫码新增访客信息", businessType = BusinessType.INSERT)
    @PostMapping("/addByScan")
    @ApiOperation(value = "扫码新增访客信息")
    public AjaxResult addByScan(@RequestBody VisitorInfo visitorInfo) {
        visitorInfo.setCreateTime(new Date());
        return toAjax(visitorInfoService.insertVisitorInfo(visitorInfo));
    }

    /**
     * 修改访客信息
     */
    @Log(title = "访客信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改访客信息")
    public AjaxResult edit(@RequestBody VisitorInfo visitorInfo) {
        return toAjax(visitorInfoService.updateVisitorInfo(visitorInfo));
    }

    /**
     * 删除访客信息
     */
    @Log(title = "访客信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除访客信息")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(visitorInfoService.deleteVisitorInfoByIds(ids));
    }

    /**
     * 小程序对内访客信息列表
     */
    @GetMapping("/listForAppInnner")
    @ApiOperation(value = "小程序对内访客信息列表")
    public TableDataInfo listForAppInnner(VisitorInfo visitorInfo) {
        startPage();
        List<VisitorInfo> list = visitorInfoService.selectVisitorInfoList(visitorInfo);
        return getDataTable(list);
    }

    /**
     * 小程序对外访客信息列表
     */
    @GetMapping("/listForAppOutter")
    @ApiOperation(value = "小程序对外访客信息列表")
    public TableDataInfo listForAppOutter(VisitorInfo visitorInfo) {
        startPage();
        visitorInfo.setCreateUserId(SecurityUtils.getUserId());
        List<VisitorInfo> list = visitorInfoService.selectVisitorInfoList(visitorInfo);
        return getDataTable(list);
    }

    /**
     * 生成二维码
     */
    @ApiOperation("生成二维码")
    @GetMapping(value = "/createQRCode")
    public AjaxResult createQRCode() {
        LocalDate now = LocalDate.now();
        // 文件名称
        String fileName = System.currentTimeMillis() + RandomUtil.randomInt(0, 100) + ".jpg";
        // 图片保存地址
        String savePath = localFilePath + "/" + now.getYear() + "/" + now.getMonthValue() + "/" + now.getDayOfMonth() + "/" + fileName;
        // 返回给前端展示二维码的地址
        String returnUrl = domain + localFilePrefix + "/" + now.getYear() + "/" + now.getMonthValue() + "/" + now.getDayOfMonth() + "/" + fileName;
        // 创建文件夹
        FileUtil.mkdir(savePath.substring(0, savePath.lastIndexOf("/")));
        // 构建二维码内容
        long currentTimeMillis = System.currentTimeMillis();
        long expirTimeMillis = currentTimeMillis + 60 * 60 * 1000;
        String content = frontDomain + "/" + "h5" + "?timestamp=" + expirTimeMillis + "&id=" + SecurityUtils.getUserId();
        // 生成二维码
        QrCodeUtil.generate(content, 300, 300, FileUtil.file(savePath));
        Map data = new HashMap();
        data.put("returnUrl", returnUrl);
        // 获取当前时间戳，存到data
        data.put("startTime", DateUtil.format(new Date(currentTimeMillis), "yyyy-MM-dd HH:mm:ss"));
        // 获取1小时后的时间戳，存到data
        data.put("endTime", DateUtil.format(new Date(expirTimeMillis), "yyyy-MM-dd HH:mm:ss"));
        return AjaxResult.success("生成二维码成功", data);
    }

}

