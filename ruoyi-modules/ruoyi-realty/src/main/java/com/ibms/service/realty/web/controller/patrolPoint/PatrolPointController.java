package com.ibms.service.realty.web.controller.patrolPoint;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.ibms.service.realty.web.domain.PatrolPoint;
import com.ibms.service.realty.web.service.PatrolPointService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 巡更点Controller
 *
 * <AUTHOR>
 * @date 2023-04-11
 */
@RestController
@RequestMapping("/patrolPoint")
public class PatrolPointController extends BaseController {
    /**
     * 资源映射路径 前缀
     */
    @Value("${file.prefix}")
    public String localFilePrefix;
    /**
     * 域名或本机访问地址
     */
    @Value("${file.domain}")
    public String domain;
    @Autowired
    private PatrolPointService patrolPointService;
    /**
     * 上传文件存储在本地的根路径
     */
    @Value("${file.path}")
    private String localFilePath;

    /**
     * 查询巡更点列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PatrolPoint patrolPoint) {
        startPage();
        List<PatrolPoint> list = patrolPointService.selectPatrolPointList(patrolPoint);
        return getDataTable(list);
    }

    /**
     * 导出巡更点列表
     */
    @Log(title = "巡更点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PatrolPoint patrolPoint) {
        List<PatrolPoint> list = patrolPointService.selectPatrolPointList(patrolPoint);
        ExcelUtil<PatrolPoint> util = new ExcelUtil<PatrolPoint>(PatrolPoint.class);
        util.exportExcel(response, list, "巡更点数据");
    }

    /**
     * 获取巡更点详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(patrolPointService.selectPatrolPointById(id));
    }

    /**
     * 新增巡更点
     */
    @Log(title = "巡更点", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PatrolPoint patrolPoint) {
        return patrolPointService.insertPatrolPoint(patrolPoint);
    }

    /**
     * 修改巡更点
     */
    @Log(title = "巡更点", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PatrolPoint patrolPoint) {
        return patrolPointService.updatePatrolPoint(patrolPoint);
    }

    /**
     * 作废/恢复
     */
    @Log(title = "作废/恢复", businessType = BusinessType.UPDATE)
    @PutMapping("/operator")
    public AjaxResult operator(@RequestBody PatrolPoint patrolPoint) {
        return toAjax(patrolPointService.operatorPatrolPoint(patrolPoint));
    }

    /**
     * 删除巡更点
     */
    @Log(title = "巡更点", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(patrolPointService.deletePatrolPointByIds(ids));
    }

    /**
     * 生成二维码
     */
    @ApiOperation("生成二维码")
    @GetMapping(value = "/createQRCode")
    public AjaxResult createQRCode(Integer id) {
        LocalDate now = LocalDate.now();
        String fileName = System.currentTimeMillis() + RandomUtil.randomInt(0, 100) + ".jpg";
        String savePath = localFilePath + "/" + now.getYear() + "/" + now.getMonthValue() + "/" + now.getDayOfMonth() + "/" + fileName;
        String returnUrl = domain + localFilePrefix + "/" + now.getYear() + "/" + now.getMonthValue() + "/" + now.getDayOfMonth() + "/" + fileName;
        FileUtil.mkdir(savePath.substring(0, savePath.lastIndexOf("/")));
        QrCodeUtil.generate(String.valueOf(id), 300, 300, FileUtil.file(savePath));
        Map data = new HashMap();
        data.put("returnUrl", returnUrl);
        return AjaxResult.success("生成二维码成功", data);
    }
}

