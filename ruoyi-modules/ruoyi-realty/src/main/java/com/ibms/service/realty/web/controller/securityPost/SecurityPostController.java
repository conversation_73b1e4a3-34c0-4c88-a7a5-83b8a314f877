package com.ibms.service.realty.web.controller.securityPost;

import com.ibms.service.realty.web.domain.SecurityPost;
import com.ibms.service.realty.web.service.SecurityPostService;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 保安岗位信息Controller
 *
 * <AUTHOR>
 * @date 2023-06-14
 */
@RestController
@RequestMapping("/securityPost")
@Api(tags = "保安岗位信息")
public class SecurityPostController extends BaseController {
    @Autowired
    private SecurityPostService securityPostService;

    /**
     * 查询保安岗位信息列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "保安岗位信息列表", notes = "保安岗位信息列表")
    public TableDataInfo list(SecurityPost securityPost) {
        startPage();
        List<SecurityPost> list = securityPostService.selectSecurityPostList(securityPost);
        return getDataTable(list);
    }

    /**
     * 导出保安岗位信息列表
     */
    @Log(title = "保安岗位信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出保安岗位信息列表", notes = "导出保安岗位信息列表")
    public void export(HttpServletResponse response, SecurityPost securityPost) {
        List<SecurityPost> list = securityPostService.selectSecurityPostList(securityPost);
        ExcelUtil<SecurityPost> util = new ExcelUtil<SecurityPost>(SecurityPost.class);
        util.exportExcel(response, list, "保安岗位信息数据");
    }

    /**
     * 获取保安岗位信息详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取保安岗位信息详细信息", notes = "获取保安岗位信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(securityPostService.selectSecurityPostById(id));
    }

//    /**
//     * 获取保安排班信息详细信息
//     */
//    @GetMapping(value = "/getScheduleInfo/{id}")
//    @ApiOperation(value = "获取保安排班信息详细信息", notes = "获取保安排班信息详细信息")
//    public AjaxResult getScheduleInfo(@PathVariable("id") Integer id) {
//        return AjaxResult.success(securityPostService.getScheduleById(id));
//    }

    /**
     * 新增保安岗位信息
     */
    @Log(title = "保安岗位信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增保安岗位信息", notes = "新增保安岗位信息")
    public AjaxResult add(@RequestBody SecurityPost securityPost) {
        return toAjax(securityPostService.insertSecurityPost(securityPost));
    }

    /**
     * 修改保安岗位信息
     */
    @Log(title = "保安岗位信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改保安岗位信息", notes = "修改保安岗位信息")
    public AjaxResult edit(@RequestBody SecurityPost securityPost) {
        return toAjax(securityPostService.updateSecurityPost(securityPost));
    }

    /**
     * 删除保安岗位信息
     */
    @Log(title = "保安岗位信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除保安岗位信息", notes = "删除保安岗位信息")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(securityPostService.deleteSecurityPostByIds(ids));
    }
}
