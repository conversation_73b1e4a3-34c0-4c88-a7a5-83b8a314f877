package com.ibms.service.realty.web.controller.cleaningPlan.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "PatrolTimesVo", description = "巡查频次vo")
public class PatrolTimesVo {

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "开始时间周几，取值范围1到7")
    private String startWeekDay;

    @ApiModelProperty(value = "结束时间周几，取值范围1到7")
    private String endWeekDay;

}
